<?php

namespace App\Services\Actions\Invoice;

use App\Enums\InvoiceDeliveryStatusEnum;
use App\Enums\PayTypeEnum;
use App\Models\Product\ProductsReservation;
use App\Models\Shopping\Invoice;
use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use App\Models\Shopping\ShoppingCart;
use App\Models\User\Address;
use App\Traits\Disconunt\AppliesDiscountCode;
use DB;

/**
 * Action class for creating a new invoice.
 */
class CreateInvoice
{
    use AppliesDiscountCode;
    /**
     * Create a new invoice for a user.
     * Creates invoice products from cart items with their details.
     * Clears the cart after successful invoice creation.
     *
     * @param array $data An array containing the address_id
     * @return Invoice The created invoice with loaded products
     */
    public function handle(array $data): Invoice
    {
        $invoice = null;
        DB::transaction(function () use ($data, &$invoice) {
            // Get the authenticated user's ID directly
            $userId = auth()->id();

            // Get the user's cart - validation is already done in the Form Request
            $cart = ShoppingCart::with('items')->where('user_id', $userId)->first();
            $address = Address::find($data['address_id']);
            $cartItems = $cart->items;

            $cartTotal = $cartItems->sum(function ($item) {
                return $item->total;
            });
            $cartSubTotal = $cartItems->sum(function ($item) {
                return $item->subtotal;
            });

            $totalDiscount = $cartItems->sum(function ($item) {
                return $item->discount;
            });
            $discountAmount = 0;
            $finalTotal = $cartTotal;
            $discountCode = null;
            if (!empty($data['discount_code'])) {
                [$discountAmount, $finalTotal, $discountCode] = $this->applyDiscountToCart($data['discount_code'], $cartTotal);
            }

            $invoiceData = [
                'user_id' => $userId,
                'invoice_number' => $this->generateInvoiceNumber(),
                'status' => PayTypeEnum::PENDING,
                'delivery_status' => InvoiceDeliveryStatusEnum::IN_PROGRESS,
                'address' => $address['address'],
                'receiver_name' => $address['receiver_name'],
                'receiver_phone' => $address['receiver_phone'],
                'province' => $address['province'],
                'city' => $address['city'],
                'zip_code' => $address['zip_code'],
                'latitude' => $address['latitude'] ?? null,
                'longitude' => $address['longitude'] ?? null,
                'subtotal' => $cartSubTotal,
                'discount_amount' => $totalDiscount + $discountAmount,
                'total' => $finalTotal,
                'discount_code_id' => $discountCode?->id,
            ];

            $invoice = Invoice::create($invoiceData);

            foreach ($cartItems as $cartItem) {
                try {

                    $product = Product::with('details')->find($cartItem->product_id);

                    $variant = ProductVariant::with('attributes')->find($cartItem->product_variant_id);

                    $invoiceProductData = [
                        'product_id' => $cartItem->product_id,
                        'product_variant_id' => $cartItem->product_variant_id,
                        'name' => $cartItem->name,
                        "sku" => $variant->sku,
                        'price' => $cartItem->price,
                        'sale_price' => $cartItem->sale_price && $cartItem->sale_price < $cartItem->price ? $cartItem->sale_price : null,
                        'quantity' => $cartItem->quantity,
                        'image' => $product->gallery()->first()->image_url,
                    ];

                    // Add guarantee data if present in cart item
                    if ($cartItem->guarantee_id) {
                        $invoiceProductData['guarantee_id'] = $cartItem->guarantee_id;
                        $invoiceProductData['guarantee_company_name'] = $cartItem->guarantee_company_name;
                        $invoiceProductData['guarantee_months'] = $cartItem->guarantee_months;
                        $invoiceProductData['guarantee_price'] = $cartItem->guarantee_price;
                    }

                    $invoiceProduct = $invoice->products()->create($invoiceProductData);


                    foreach ($variant->attributes as $attribute) {
                        $invoiceProduct->details()->create([
                            'key' => $attribute->attribute_title,
                            'value' => $attribute->attribute_value,
                        ]);
                    }



                    ProductsReservation::create([
                        'user_id' => $cart->user_id,
                        'invoice_id' => $invoice->id,
                        'product_variant_id' => $cartItem->product_variant_id,
                        'quantity' => $cartItem->quantity,
                        'expire_date' => now()->addMinute(),
                    ]);
                } catch (\Exception $e) {
                    throw $e;
                }
            }

            // Clear the cart
            $cart->items()->delete();
        });

        $invoice->load('products.details');

        return $invoice;
    }
    private function generateInvoiceNumber(): string
    {
        do {
            $number = random_int(10000000, 99999999);
        } while (Invoice::where('invoice_number', $number)->exists());

        return (string) $number;
    }
}
