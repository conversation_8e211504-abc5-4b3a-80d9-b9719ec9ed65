<?php
namespace App\Services\Actions\Cart;

use App\Models\Product\ProductVariant;
use App\Models\Shopping\ShoppingCart;
use App\Traits\Cart\CalculatesCartPricing;

/**
 * AddToCart Action
 *
 * Handles adding products to a user's shopping cart.
 * This action is responsible for:
 * - Creating or updating cart items
 * - Handling authenticated user carts
 */
class AddToCart
{
    use CalculatesCartPricing;
    /**
     * Process adding an item to the cart.
     *
     * @param array $data Input data containing:
     *                    - variant_id: (string) The ID of the product variant to add
     *                    - quantity: (int) The quantity to add, defaults to 1
     *                    - product: The product model (added by the Form Request)
     *                    - variant: The variant model (added by the Form Request)
     * @return array The updated shopping cart with the new item added
     */
    public function handle(array $data): array
    {
        // Get the authenticated user's ID directly
        $userId = auth()->id();
        $quantity = $data['quantity'] ?? 1;

        $variant = ProductVariant::find($data['variant_id']);
        $product = $variant->product;

        // Get or create a shopping cart for this user
        $cart = ShoppingCart::firstOrCreate(['user_id' => $userId]);

        // Check if this product variant is already in the cart
        $existing = $cart->items()
            ->where('product_id', $product->id)
            ->where('product_variant_id', $variant->id)
            ->first();

        if ($existing) {
            // If item already exists in cart, update the quantity and guarantee if provided
            // Stock validation is already done in the Form Request
            $newQty = $existing->quantity + $quantity;

            // Update the quantity
            $existing->quantity = $newQty;

            // Update guarantee data if provided
            if (!empty($data['guarantee_id'])) {
                $guarantee = \App\Models\Product\Guarantee::find($data['guarantee_id']);
                if ($guarantee) {
                    $existing->guarantee_id = $guarantee->id;
                    $existing->guarantee_company_name = $guarantee->company_name;
                    $existing->guarantee_months = $guarantee->months;
                    $existing->guarantee_price = $guarantee->price;
                }
            }

            $existing->save();
        } else {
            // If item doesn't exist in cart, create a new cart item
            // We store a snapshot of product data to preserve pricing and details
            $cartItemData = [
                'product_id' => $product->id,
                'product_variant_id' => $variant->id,
                'name' => $product->title,
                'price' => $variant->price,
                'sale_price' => $variant->sale_price,
                'quantity' => $quantity,
                'image' => $variant->image ?? $product->main_image ?? null,
            ];

            // Add guarantee data if provided
            if (!empty($data['guarantee_id'])) {
                $guarantee = \App\Models\Product\Guarantee::find($data['guarantee_id']);
                if ($guarantee) {
                    $cartItemData['guarantee_id'] = $guarantee->id;
                    $cartItemData['guarantee_company_name'] = $guarantee->company_name;
                    $cartItemData['guarantee_months'] = $guarantee->months;
                    $cartItemData['guarantee_price'] = $guarantee->price;
                }
            }

            $cart->items()->create($cartItemData);
        }
        $cart->refresh();
        $this->loadCartRelationships($cart);

        return $this->formatCartData($cart);
    }
}
